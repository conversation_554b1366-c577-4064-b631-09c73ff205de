*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[08:56:54] 




[08:56:54] Extension host agent started.
[08:56:54] [<unknown>][5b59480c][ExtensionHostConnection] New connection established.
[08:56:54] [<unknown>][4b2003ca][ManagementConnection] New connection established.
[08:56:54] [<unknown>][5b59480c][ExtensionHostConnection] <1086> Launched Extension Host Process.
[08:57:15] [<unknown>][4b2003ca][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[08:57:15] [<unknown>][5b59480c][ExtensionHostConnection] <1086> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[08:57:15] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[08:57:15] Last EH closed, waiting before shutting down
[08:57:18] [<unknown>][2790e157][ManagementConnection] New connection established.
[08:57:18] [<unknown>][371943a5][ExtensionHostConnection] New connection established.
[08:57:18] [<unknown>][371943a5][ExtensionHostConnection] <1367> Launched Extension Host Process.
