2025-07-31 08:57:02.974 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-07-31 08:57:02.974 [info] Can't use the Electron fetcher in this environment.
2025-07-31 08:57:02.974 [info] Using the Node fetch fetcher.
2025-07-31 08:57:02.974 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-07-31 08:57:02.974 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-07-31 08:57:02.974 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-07-31 08:57:04.495 [info] Logged in as dipeshbhanu
2025-07-31 08:57:06.269 [info] Got Copilot token for dipeshbhanu
2025-07-31 08:57:06.278 [info] activationBlocker from 'languageModelAccess' took for 2448ms
2025-07-31 08:57:06.746 [info] copilot token chat_enabled: true, sku: free_limited_copilot
2025-07-31 08:57:06.746 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-07-31 08:57:06.767 [info] Registering default platform agent...
2025-07-31 08:57:06.768 [info] activationBlocker from 'conversationFeature' took for 2944ms
2025-07-31 08:57:07.174 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-07-31 08:57:07.420 [info] Fetched model metadata in 1143ms 62f2feb9-1d61-41d5-a5c4-6d98133314f7
