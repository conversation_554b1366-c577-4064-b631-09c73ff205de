2025-07-31 08:56:56.078 [info] Extension host with pid 1086 started
2025-07-31 08:56:56.558 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-07-31 08:56:56.680 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-07-31 08:56:56.680 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-07-31 08:56:56.912 [info] Eager extensions activated
2025-07-31 08:56:56.912 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 08:56:56.913 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 08:56:56.914 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 08:56:56.914 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 08:56:56.914 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 08:57:00.957 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-07-31 08:57:09.531 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5CAnita%20Bhanushali%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at i8e.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:523:10721)
2025-07-31 08:57:15.242 [info] Extension host terminating: received terminate message from renderer
2025-07-31 08:57:15.306 [info] Extension host with pid 1086 exiting with code 0
