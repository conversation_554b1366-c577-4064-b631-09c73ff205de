2025-07-31 08:57:26.926 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-07-31 08:57:26.926 [info] Can't use the Electron fetcher in this environment.
2025-07-31 08:57:26.926 [info] Using the Node fetch fetcher.
2025-07-31 08:57:26.926 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-07-31 08:57:26.926 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-07-31 08:57:26.926 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-07-31 08:57:29.397 [info] Logged in as dipeshbhanu
2025-07-31 08:57:30.940 [info] Got Copilot token for dipeshbhanu
2025-07-31 08:57:30.950 [info] activationBlocker from 'languageModelAccess' took for 3797ms
2025-07-31 08:57:31.355 [info] copilot token chat_enabled: true, sku: free_limited_copilot
2025-07-31 08:57:31.355 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-07-31 08:57:31.376 [info] Registering default platform agent...
2025-07-31 08:57:31.377 [info] activationBlocker from 'conversationFeature' took for 4229ms
2025-07-31 08:57:31.745 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-07-31 08:57:31.979 [info] Fetched model metadata in 1030ms 5ecc26e4-d18a-44a6-83e7-9eccba5479d1
