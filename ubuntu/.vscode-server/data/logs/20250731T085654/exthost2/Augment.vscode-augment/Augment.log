2025-07-31 08:57:26.905 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-31 08:57:26.905 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-07-31 08:57:26.905 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"notificationPollingIntervalMs":180000,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"enableViewedContentTracking":false,"viewedContentCloseRangeThreshold":5,"viewedContentDiscreteJumpThreshold":15,"viewedContentMinEventAgeMs":1000,"viewedContentMaxEventAgeMs":3600000,"viewedContentMaxTrackedFiles":10,"viewedContentMaxSameFileEntries":2,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"truncationFooterAdditionText":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","vscodeMinVersion":"1.96.0","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"enableAgentSwarmMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryParams":"","enableCommitIndexing":false,"maxCommitsToIndex":0,"enableExchangeStorage":false,"conversationHistorySizeThresholdBytes":268435456,"enableToolUseStateStorage":false,"retryChatStreamTimeouts":false,"remoteAgentCurrentWorkspace":false,"enableMemoryRetrieval":false,"enableAgentTabs":false,"enableSwarmMode":false,"enableGroupedTools":false,"remoteAgentsResumeHintAvailableTtlDays":0,"enableParallelTools":false,"enableAgentGitTracker":false,"enableNativeRemoteMcp":true,"vscodeTerminalStrategy":"vscode_events"}
2025-07-31 08:57:26.905 [info] 'AugmentExtension' Retrieving model config
2025-07-31 08:57:27.516 [info] 'AugmentExtension' Retrieved model config
2025-07-31 08:57:27.516 [info] 'AugmentExtension' Returning model config
2025-07-31 08:57:27.589 [info] 'SidecarAnalytics' Segment analytics initialized for vscode in tenant i0
2025-07-31 08:57:27.589 [info] 'FeatureFlagManager' feature flags changed:
  - notificationPollingIntervalMs: 180000 to 60000
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - viewedContentMaxEventAgeMs: 3600000 to 30000
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - maxLinesTerminalProcessOutput: 0 to 20
  - truncationFooterAdditionText: "" to "**Only use view-range-untruncated or search-untruncated tools if additional output is strictly necessary to continue**, such as when:\n- You need to find specific error details that are clearly truncated\n- You need to search for specific patterns or text that might be elsewhere in the output\n- The truncated output is genuinely insufficient for the task at hand\n\nIf you do need to use these tools:\n- For view-range-untruncated: Request only the specific line ranges you actually need\n- For search-untruncated: Use specific search terms rather than viewing large ranges\n"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - vscodeDirectApplyMinVersion: "" to "0.499.0"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - enableRules: false to true
  - memoriesTextEditorEnabled: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryParams: "" to "{\"buffer_time_before_cache_expiration_ms\": 30000, \"cache_ttl_ms\": 300000, \"history_tail_size_chars_to_exclude\": 80000, \"prompt\": \"Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\\n\\nYour summary should be structured as follows:\\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\\n\\nExample summary structure:\\n1. Previous Conversation:\\n[Detailed description]\\n2. Current Work:\\n[Detailed description]\\n3. Key Technical Concepts:\\n- [Concept 1]\\n- [Concept 2]\\n- [...]\\n4. Relevant Files and Code:\\n- [File Name 1]\\n    - [Summary of why this file is important]\\n    - [Summary of the changes made to this file, if any]\\n    - [Important Code Snippet]\\n- [File Name 2]\\n    - [Important Code Snippet]\\n- [...]\\n5. Problem Solving:\\n[Detailed description]\\n6. Pending Tasks and Next Steps:\\n- [Task 1 details & next steps]\\n- [Task 2 details & next steps]\\n- [...]\\n\\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\\n\", \"trigger_on_history_size_chars\": 200000, \"trigger_on_history_size_chars_when_cache_expiring\": 140000}"
  - enableCommitIndexing: false to true
  - maxCommitsToIndex: 0 to 1000
  - retryChatStreamTimeouts: false to true
  - remoteAgentsResumeHintAvailableTtlDays: 0 to 21
2025-07-31 08:57:27.589 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 7/31/2025, 8:51:14 AM
2025-07-31 08:57:27.589 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-07-31 08:57:27.589 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-07-31 08:57:27.589 [info] 'SyncingPermissionTracker' Permission to sync folder /home granted at 7/31/2025, 8:51:14 AM; type = explicit
2025-07-31 08:57:27.589 [info] 'WorkspaceManager' Adding workspace folder home; folderRoot = /home; syncingPermission = granted
2025-07-31 08:57:27.589 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 7/31/2025, 8:51:14 AM
2025-07-31 08:57:27.649 [info] 'AugmentExtension' Git tracking disabled by enableAgentGitTracker feature flag
2025-07-31 08:57:27.649 [info] 'MemoryWebviewMessageHandler' Memory webview message handler initialized
2025-07-31 08:57:27.649 [info] 'NotificationWatcher' Starting notification polling with interval 60000ms
2025-07-31 08:57:27.649 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-07-31 08:57:27.649 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-07-31 08:57:27.649 [info] 'HotKeyHints' HotKeyHints initialized
2025-07-31 08:57:27.654 [info] 'AugmentExtension' Git tracking disabled by enableAgentGitTracker feature flag
2025-07-31 08:57:27.655 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-07-31 08:57:27.724 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-31 08:57:27.725 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-31 08:57:28.057 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-07-31 08:57:28.057 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-07-31 08:57:28.599 [info] 'DynamicLevelKvStore' Ensuring LevelDB is initialized
2025-07-31 08:57:28.600 [info] 'WorkspaceManager[home]' Start tracking
2025-07-31 08:57:28.654 [info] 'PathMap' Opened source folder /home with id 100
2025-07-31 08:57:28.654 [info] 'OpenFileManager' Opened source folder 100
2025-07-31 08:57:28.656 [info] 'MtimeCache[home]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/6ae2b767342d715947fbdf667f32e8cf/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-07-31 08:57:28.849 [info] 'MtimeCache[home]' read 2915 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/6ae2b767342d715947fbdf667f32e8cf/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-07-31 08:57:29.192 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1124.144957,"timestamp":"2025-07-31T08:57:29.181Z"}]
2025-07-31 08:57:29.212 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-31 08:57:29.212 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-31 08:57:29.383 [info] 'AugmentExtension' Git tracking disabled by enableAgentGitTracker feature flag
2025-07-31 08:57:29.521 [info] 'TaskManager' Setting current root task UUID to 74db3fc9-62b8-4052-8580-c434f3afa46b
2025-07-31 08:57:29.521 [info] 'TaskManager' Setting current root task UUID to 74db3fc9-62b8-4052-8580-c434f3afa46b
2025-07-31 08:57:29.920 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-31 08:57:29.920 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-31 08:57:29.920 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-31 08:57:29.920 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-31 08:57:29.990 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250731T085654/exthost2/output_logging_20250731T085719
2025-07-31 09:00:29.293 [info] 'WorkspaceManager[home]' Tracking enabled
2025-07-31 09:00:29.293 [info] 'WorkspaceManager[home]' Path metrics:
  - directories emitted: 801
  - files emitted: 3146
  - other paths emitted: 0
  - total paths emitted: 3947
  - timing stats:
    - readDir: 12 ms
    - filter: 102 ms
    - yield: 20 ms
    - total: 151 ms
2025-07-31 09:00:29.293 [info] 'WorkspaceManager[home]' File metrics:
  - paths accepted: 3135
  - paths not accessible: 0
  - not plain files: 0
  - large files: 80
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 2903
  - mtime cache misses: 232
  - probe batches: 62
  - blob names probed: 5033
  - files read: 403
  - blobs uploaded: 36
  - timing stats:
    - ingestPath: 24 ms
    - probe: 34684 ms
    - stat: 32 ms
    - read: 1402 ms
    - upload: 3347 ms
2025-07-31 09:00:29.293 [info] 'WorkspaceManager[home]' Startup metrics:
  - create SourceFolder: 57 ms
  - read MtimeCache: 193 ms
  - pre-populate PathMap: 149 ms
  - create PathFilter: 227 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 155 ms
  - purge stale PathMap entries: 2 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 179906 ms
  - enable persist: 4 ms
  - total: 180693 ms
2025-07-31 09:00:29.293 [info] 'WorkspaceManager' Workspace startup complete in 181720 ms
