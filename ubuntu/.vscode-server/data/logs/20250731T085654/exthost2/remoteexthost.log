2025-07-31 08:57:19.315 [info] Extension host with pid 1367 started
2025-07-31 08:57:19.316 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/6ae2b767342d715947fbdf667f32e8cf/vscode.lock'
2025-07-31 08:57:19.316 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/6ae2b767342d715947fbdf667f32e8cf/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-07-31 08:57:19.322 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/6ae2b767342d715947fbdf667f32e8cf/vscode.lock': The pid 1873 appears to be gone.
2025-07-31 08:57:19.323 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/6ae2b767342d715947fbdf667f32e8cf/vscode.lock': Deleting a stale lock.
2025-07-31 08:57:19.330 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/6ae2b767342d715947fbdf667f32e8cf/vscode.lock': Lock acquired.
2025-07-31 08:57:19.692 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-07-31 08:57:19.694 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-07-31 08:57:19.989 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-07-31 08:57:19.989 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-07-31 08:57:20.348 [info] Eager extensions activated
2025-07-31 08:57:20.348 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 08:57:20.349 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 08:57:20.349 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 08:57:20.349 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 08:57:20.350 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-07-31 08:57:33.948 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5CAnita%20Bhanushali%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at i8e.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:523:10721)
