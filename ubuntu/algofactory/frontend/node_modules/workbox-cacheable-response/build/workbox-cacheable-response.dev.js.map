{"version": 3, "file": "workbox-cacheable-response.dev.js", "sources": ["../_version.js", "../CacheableResponse.js", "../CacheableResponsePlugin.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:cacheable-response:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport './_version.js';\n/**\n * This class allows you to set up rules determining what\n * status codes and/or headers need to be present in order for a\n * [`Response`](https://developer.mozilla.org/en-US/docs/Web/API/Response)\n * to be considered cacheable.\n *\n * @memberof workbox-cacheable-response\n */\nclass CacheableResponse {\n    /**\n     * To construct a new CacheableResponse instance you must provide at least\n     * one of the `config` properties.\n     *\n     * If both `statuses` and `headers` are specified, then both conditions must\n     * be met for the `Response` to be considered cacheable.\n     *\n     * @param {Object} config\n     * @param {Array<number>} [config.statuses] One or more status codes that a\n     * `Response` can have and be considered cacheable.\n     * @param {Object<string,string>} [config.headers] A mapping of header names\n     * and expected values that a `Response` can have and be considered cacheable.\n     * If multiple headers are provided, only one needs to be present.\n     */\n    constructor(config = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(config.statuses || config.headers)) {\n                throw new WorkboxError('statuses-or-headers-required', {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.statuses) {\n                assert.isArray(config.statuses, {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                    paramName: 'config.statuses',\n                });\n            }\n            if (config.headers) {\n                assert.isType(config.headers, 'object', {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                    paramName: 'config.headers',\n                });\n            }\n        }\n        this._statuses = config.statuses;\n        this._headers = config.headers;\n    }\n    /**\n     * Checks a response to see whether it's cacheable or not, based on this\n     * object's configuration.\n     *\n     * @param {Response} response The response whose cacheability is being\n     * checked.\n     * @return {boolean} `true` if the `Response` is cacheable, and `false`\n     * otherwise.\n     */\n    isResponseCacheable(response) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(response, Response, {\n                moduleName: 'workbox-cacheable-response',\n                className: 'CacheableResponse',\n                funcName: 'isResponseCacheable',\n                paramName: 'response',\n            });\n        }\n        let cacheable = true;\n        if (this._statuses) {\n            cacheable = this._statuses.includes(response.status);\n        }\n        if (this._headers && cacheable) {\n            cacheable = Object.keys(this._headers).some((headerName) => {\n                return response.headers.get(headerName) === this._headers[headerName];\n            });\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (!cacheable) {\n                logger.groupCollapsed(`The request for ` +\n                    `'${getFriendlyURL(response.url)}' returned a response that does ` +\n                    `not meet the criteria for being cached.`);\n                logger.groupCollapsed(`View cacheability criteria here.`);\n                logger.log(`Cacheable statuses: ` + JSON.stringify(this._statuses));\n                logger.log(`Cacheable headers: ` + JSON.stringify(this._headers, null, 2));\n                logger.groupEnd();\n                const logFriendlyHeaders = {};\n                response.headers.forEach((value, key) => {\n                    logFriendlyHeaders[key] = value;\n                });\n                logger.groupCollapsed(`View response status and headers here.`);\n                logger.log(`Response status: ${response.status}`);\n                logger.log(`Response headers: ` + JSON.stringify(logFriendlyHeaders, null, 2));\n                logger.groupEnd();\n                logger.groupCollapsed(`View full response details here.`);\n                logger.log(response.headers);\n                logger.log(response);\n                logger.groupEnd();\n                logger.groupEnd();\n            }\n        }\n        return cacheable;\n    }\n}\nexport { CacheableResponse };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { CacheableResponse, } from './CacheableResponse.js';\nimport './_version.js';\n/**\n * A class implementing the `cacheWillUpdate` lifecycle callback. This makes it\n * easier to add in cacheability checks to requests made via Workbox's built-in\n * strategies.\n *\n * @memberof workbox-cacheable-response\n */\nclass CacheableResponsePlugin {\n    /**\n     * To construct a new CacheableResponsePlugin instance you must provide at\n     * least one of the `config` properties.\n     *\n     * If both `statuses` and `headers` are specified, then both conditions must\n     * be met for the `Response` to be considered cacheable.\n     *\n     * @param {Object} config\n     * @param {Array<number>} [config.statuses] One or more status codes that a\n     * `Response` can have and be considered cacheable.\n     * @param {Object<string,string>} [config.headers] A mapping of header names\n     * and expected values that a `Response` can have and be considered cacheable.\n     * If multiple headers are provided, only one needs to be present.\n     */\n    constructor(config) {\n        /**\n         * @param {Object} options\n         * @param {Response} options.response\n         * @return {Response|null}\n         * @private\n         */\n        this.cacheWillUpdate = async ({ response }) => {\n            if (this._cacheableResponse.isResponseCacheable(response)) {\n                return response;\n            }\n            return null;\n        };\n        this._cacheableResponse = new CacheableResponse(config);\n    }\n}\nexport { CacheableResponsePlugin };\n"], "names": ["self", "_", "e", "CacheableResponse", "constructor", "config", "statuses", "headers", "WorkboxError", "moduleName", "className", "funcName", "assert", "isArray", "paramName", "isType", "_statuses", "_headers", "isResponseCacheable", "response", "isInstance", "Response", "cacheable", "includes", "status", "Object", "keys", "some", "headerName", "get", "logger", "groupCollapsed", "getFriendlyURL", "url", "log", "JSON", "stringify", "groupEnd", "logFriendlyHeaders", "for<PERSON>ach", "value", "key", "CacheableResponsePlugin", "cacheWillUpdate", "_cacheableResponse"], "mappings": ";;;;IAEA,IAAI;IACAA,EAAAA,IAAI,CAAC,kCAAD,CAAJ,IAA4CC,CAAC,EAA7C;IACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMC,iBAAN,CAAwB;IACpB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIC,EAAAA,WAAW,CAACC,MAAM,GAAG,EAAV,EAAc;IACrB,IAA2C;IACvC,UAAI,EAAEA,MAAM,CAACC,QAAP,IAAmBD,MAAM,CAACE,OAA5B,CAAJ,EAA0C;IACtC,cAAM,IAAIC,4BAAJ,CAAiB,8BAAjB,EAAiD;IACnDC,UAAAA,UAAU,EAAE,4BADuC;IAEnDC,UAAAA,SAAS,EAAE,mBAFwC;IAGnDC,UAAAA,QAAQ,EAAE;IAHyC,SAAjD,CAAN;IAKH;;IACD,UAAIN,MAAM,CAACC,QAAX,EAAqB;IACjBM,QAAAA,gBAAM,CAACC,OAAP,CAAeR,MAAM,CAACC,QAAtB,EAAgC;IAC5BG,UAAAA,UAAU,EAAE,4BADgB;IAE5BC,UAAAA,SAAS,EAAE,mBAFiB;IAG5BC,UAAAA,QAAQ,EAAE,aAHkB;IAI5BG,UAAAA,SAAS,EAAE;IAJiB,SAAhC;IAMH;;IACD,UAAIT,MAAM,CAACE,OAAX,EAAoB;IAChBK,QAAAA,gBAAM,CAACG,MAAP,CAAcV,MAAM,CAACE,OAArB,EAA8B,QAA9B,EAAwC;IACpCE,UAAAA,UAAU,EAAE,4BADwB;IAEpCC,UAAAA,SAAS,EAAE,mBAFyB;IAGpCC,UAAAA,QAAQ,EAAE,aAH0B;IAIpCG,UAAAA,SAAS,EAAE;IAJyB,SAAxC;IAMH;IACJ;;IACD,SAAKE,SAAL,GAAiBX,MAAM,CAACC,QAAxB;IACA,SAAKW,QAAL,GAAgBZ,MAAM,CAACE,OAAvB;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACIW,EAAAA,mBAAmB,CAACC,QAAD,EAAW;IAC1B,IAA2C;IACvCP,MAAAA,gBAAM,CAACQ,UAAP,CAAkBD,QAAlB,EAA4BE,QAA5B,EAAsC;IAClCZ,QAAAA,UAAU,EAAE,4BADsB;IAElCC,QAAAA,SAAS,EAAE,mBAFuB;IAGlCC,QAAAA,QAAQ,EAAE,qBAHwB;IAIlCG,QAAAA,SAAS,EAAE;IAJuB,OAAtC;IAMH;;IACD,QAAIQ,SAAS,GAAG,IAAhB;;IACA,QAAI,KAAKN,SAAT,EAAoB;IAChBM,MAAAA,SAAS,GAAG,KAAKN,SAAL,CAAeO,QAAf,CAAwBJ,QAAQ,CAACK,MAAjC,CAAZ;IACH;;IACD,QAAI,KAAKP,QAAL,IAAiBK,SAArB,EAAgC;IAC5BA,MAAAA,SAAS,GAAGG,MAAM,CAACC,IAAP,CAAY,KAAKT,QAAjB,EAA2BU,IAA3B,CAAiCC,UAAD,IAAgB;IACxD,eAAOT,QAAQ,CAACZ,OAAT,CAAiBsB,GAAjB,CAAqBD,UAArB,MAAqC,KAAKX,QAAL,CAAcW,UAAd,CAA5C;IACH,OAFW,CAAZ;IAGH;;IACD,IAA2C;IACvC,UAAI,CAACN,SAAL,EAAgB;IACZQ,QAAAA,gBAAM,CAACC,cAAP,CAAuB,kBAAD,GACjB,IAAGC,gCAAc,CAACb,QAAQ,CAACc,GAAV,CAAe,kCADf,GAEjB,yCAFL;IAGAH,QAAAA,gBAAM,CAACC,cAAP,CAAuB,kCAAvB;IACAD,QAAAA,gBAAM,CAACI,GAAP,CAAY,sBAAD,GAAyBC,IAAI,CAACC,SAAL,CAAe,KAAKpB,SAApB,CAApC;IACAc,QAAAA,gBAAM,CAACI,GAAP,CAAY,qBAAD,GAAwBC,IAAI,CAACC,SAAL,CAAe,KAAKnB,QAApB,EAA8B,IAA9B,EAAoC,CAApC,CAAnC;IACAa,QAAAA,gBAAM,CAACO,QAAP;IACA,cAAMC,kBAAkB,GAAG,EAA3B;IACAnB,QAAAA,QAAQ,CAACZ,OAAT,CAAiBgC,OAAjB,CAAyB,CAACC,KAAD,EAAQC,GAAR,KAAgB;IACrCH,UAAAA,kBAAkB,CAACG,GAAD,CAAlB,GAA0BD,KAA1B;IACH,SAFD;IAGAV,QAAAA,gBAAM,CAACC,cAAP,CAAuB,wCAAvB;IACAD,QAAAA,gBAAM,CAACI,GAAP,CAAY,oBAAmBf,QAAQ,CAACK,MAAO,EAA/C;IACAM,QAAAA,gBAAM,CAACI,GAAP,CAAY,oBAAD,GAAuBC,IAAI,CAACC,SAAL,CAAeE,kBAAf,EAAmC,IAAnC,EAAyC,CAAzC,CAAlC;IACAR,QAAAA,gBAAM,CAACO,QAAP;IACAP,QAAAA,gBAAM,CAACC,cAAP,CAAuB,kCAAvB;IACAD,QAAAA,gBAAM,CAACI,GAAP,CAAWf,QAAQ,CAACZ,OAApB;IACAuB,QAAAA,gBAAM,CAACI,GAAP,CAAWf,QAAX;IACAW,QAAAA,gBAAM,CAACO,QAAP;IACAP,QAAAA,gBAAM,CAACO,QAAP;IACH;IACJ;;IACD,WAAOf,SAAP;IACH;;IAhGmB;;ICpBxB;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMoB,uBAAN,CAA8B;IAC1B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACItC,EAAAA,WAAW,CAACC,MAAD,EAAS;IAChB;IACR;IACA;IACA;IACA;IACA;IACQ,SAAKsC,eAAL,GAAuB,OAAO;IAAExB,MAAAA;IAAF,KAAP,KAAwB;IAC3C,UAAI,KAAKyB,kBAAL,CAAwB1B,mBAAxB,CAA4CC,QAA5C,CAAJ,EAA2D;IACvD,eAAOA,QAAP;IACH;;IACD,aAAO,IAAP;IACH,KALD;;IAMA,SAAKyB,kBAAL,GAA0B,IAAIzC,iBAAJ,CAAsBE,MAAtB,CAA1B;IACH;;IA7ByB;;;;;;;;;;;"}